<template>
  <div class="vue-office-preview">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <span class="file-name">{{ fileName }}</span>
        <el-tag :type="getFileTypeColor(fileType) || undefined" size="small">
          {{ fileType.toUpperCase() }}
        </el-tag>
      </div>
      <div class="toolbar-right">
        <!-- 缩放控制按钮 -->
        <div class="zoom-controls">
          <el-button-group size="small">
            <el-button @click="zoomOut" :disabled="scale <= 0.25">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button disabled class="zoom-display">
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button @click="zoomIn" :disabled="scale >= 3">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
          <el-button size="small" @click="resetZoom" style="margin-left: 8px;">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        <!-- <el-button size="small" type="primary" @click="downloadFile" v-if="supportDownload">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
        <el-button size="small" @click="$emit('close')">
          <el-icon><Close /></el-icon>
          关闭
        </el-button> -->
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content" ref="previewContent">
      <!-- Word文档预览 -->
      <div v-if="isWordDocument && fileArrayBuffer" class="document-container">
        <vue-office-docx
          ref="docxRef"
          :src="fileArrayBuffer"
          :style="documentContentStyle"
          @rendered="onRendered"
          @error="onError"
        />
      </div>

      <!-- Excel文档预览 -->
      <div v-else-if="isExcelDocument && fileArrayBuffer" class="document-container">
        <vue-office-excel
          ref="excelRef"
          :src="fileArrayBuffer"
          :style="documentContentStyle"
          @rendered="onRendered"
          @error="onError"
        />
      </div>

      <!-- PDF文档预览 -->
      <div v-else-if="isPdfDocument && fileArrayBuffer" class="pdf-container">
        <div class="document-container">
          <vue-office-pdf
            ref="pdfRef"
            :src="fileArrayBuffer"
            :style="documentContentStyle"
            @rendered="onRendered"
            @error="onError"
          />
        </div>
      </div>

      <!-- PowerPoint文档预览 -->
      <div v-else-if="isPowerPointDocument" class="ppt-preview">
        <el-result
          icon="warning"
          title="PowerPoint预览"
          sub-title="vue-office暂不支持PPT预览，建议下载后查看"
        >
          <template #extra>
            <el-button type="primary" @click="downloadFile">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
          </template>
        </el-result>
      </div>

      <!-- 图片预览 -->
      <div v-else-if="isImageFile" class="image-preview">
        <el-image
          :src="imageUrl"
          fit="contain"
          style="width: 100%; height: 600px;"
          :preview-src-list="[imageUrl]"
          :initial-index="0"
          preview-teleported
        />
      </div>

      <!-- 文本文件预览 -->
      <div v-else-if="isTextFile" class="text-preview">
        <pre class="text-content">{{ textContent }}</pre>
      </div>

      <!-- 加载中状态 -->
      <div v-else-if="isLoading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p>正在加载文件预览...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-container">
        <el-result
          icon="error"
          title="预览失败"
          :sub-title="errorMessage"
        >
          <template #extra>
            <el-button type="primary" @click="retryPreview">重试</el-button>
            <el-button @click="downloadFile">下载文件</el-button>
          </template>
        </el-result>
      </div>

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-preview">
        <el-result
          icon="info"
          title="文件类型不支持预览"
          :sub-title="`文件类型: ${fileType.toUpperCase()}`"
        >
          <template #extra>
            <el-button type="primary" @click="downloadFile">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
            <el-button @click="$emit('close')">关闭</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Download, Loading, ZoomIn, ZoomOut, Refresh } from '@element-plus/icons-vue';

// 导入vue-office组件 - 使用Vue3版本
import VueOfficeDocx from '@vue-office/docx/lib/v3/index.js';
import VueOfficeExcel from '@vue-office/excel/lib/v3/index.js';
import VueOfficePdf from '@vue-office/pdf/lib/v3/index.js';

// 导入样式 (PDF组件不需要CSS文件)
import '@vue-office/docx/lib/v3/index.css';
import '@vue-office/excel/lib/v3/index.css';

// Props
interface Props {
  fileName: string;
  fileType: string;
  fileData: string; // base64编码的文件数据
  originalRow?: any; // 原始行数据，用于获取文件ID
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const isLoading = ref(true);
const hasError = ref(false);
const errorMessage = ref('');
const fileArrayBuffer = ref<ArrayBuffer | null>(null);
const imageUrl = ref('');
const textContent = ref('');
const actualFileData = ref(''); // 实际的文件数据

// 缩放相关数据
const scale = ref(1); // 缩放比例，默认100%
const previewContent = ref<HTMLElement | null>(null);

// 计算属性 - 文件类型判断
const isWordDocument = computed(() => {
  return ['doc', 'docx'].includes(props.fileType.toLowerCase());
});

const isExcelDocument = computed(() => {
  return ['xls', 'xlsx'].includes(props.fileType.toLowerCase());
});

const isPdfDocument = computed(() => {
  return props.fileType.toLowerCase() === 'pdf';
});

const isPowerPointDocument = computed(() => {
  return ['ppt', 'pptx'].includes(props.fileType.toLowerCase());
});

const isImageFile = computed(() => {
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(props.fileType.toLowerCase());
});

const isTextFile = computed(() => {
  return ['txt', 'json', 'xml', 'csv', 'log', 'md'].includes(props.fileType.toLowerCase());
});

const supportDownload = computed(() => {
  return true; // 所有文件都支持下载
});

// 文档内容样式计算属性
const documentContentStyle = computed(() => {
  return {
    height: '500px', // 固定原始高度
    width: '100%',
    transform: `scale(${scale.value})`,
    transformOrigin: 'top center',
    transition: 'transform 0.3s ease'
  };
});

// 获取文件类型标签颜色
const getFileTypeColor = (type: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' | null => {
  const colorMap: { [key: string]: 'primary' | 'success' | 'info' | 'warning' | 'danger' } = {
    'pdf': 'danger',
    'doc': 'primary',
    'docx': 'primary',
    'xls': 'success',
    'xlsx': 'success',
    'ppt': 'warning',
    'pptx': 'warning',
    'jpg': 'info',
    'jpeg': 'info',
    'png': 'info',
    'gif': 'info'
  };
  return colorMap[type.toLowerCase()] || null;
};

// 将base64转换为ArrayBuffer
const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binaryString = window.atob(base64);
  const bytes = new Uint8Array(binaryString.length);

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  return bytes.buffer;
};

// 创建图片URL
const createImageUrl = (base64: string): string => {
  const mimeType = getMimeType(props.fileType);
  return `data:${mimeType};base64,${base64}`;
};

// 获取MIME类型
const getMimeType = (fileType: string): string => {
  const mimeTypes: { [key: string]: string } = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'txt': 'text/plain',
    'json': 'application/json',
    'xml': 'application/xml',
    'csv': 'text/csv'
  };
  return mimeTypes[fileType.toLowerCase()] || 'application/octet-stream';
};

// 加载文件数据
const loadFileData = async () => {
  if (!props.originalRow?.id) {
    throw new Error('缺少文件ID');
  }

  try {
    // 动态导入http工具
    const { http } = await import('@/utils/http');

    const response = await http.get(`/archivesFile/download?idList=${props.originalRow.id}`) as any;

    if (!response || !response.data || !response.data.file) {
      throw new Error('文件数据格式错误');
    }

    return response.data.file;
  } catch (error) {
    console.error('文件加载失败:', error);
    throw error;
  }
};

// 初始化预览
const initPreview = async () => {
  try {
    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    let fileData = props.fileData;

    // 如果没有文件数据，则从服务器加载
    if (!fileData && props.originalRow?.id) {
      fileData = await loadFileData();
      actualFileData.value = fileData;
    } else {
      actualFileData.value = fileData;
    }

    if (!fileData) {
      throw new Error('文件数据为空');
    }

    // 根据文件类型处理数据
    if (isWordDocument.value || isExcelDocument.value || isPdfDocument.value) {
      // Office文档和PDF需要ArrayBuffer格式
      fileArrayBuffer.value = base64ToArrayBuffer(fileData);
    } else if (isImageFile.value) {
      // 图片文件创建data URL
      imageUrl.value = createImageUrl(fileData);
    } else if (isTextFile.value) {
      // 文本文件直接解码
      const binaryString = window.atob(fileData);
      textContent.value = binaryString;
    }

    isLoading.value = false;
  } catch (error) {
    console.error('预览初始化失败:', error);
    hasError.value = true;
    errorMessage.value = error instanceof Error ? error.message : '未知错误';
    isLoading.value = false;
  }
};

// vue-office事件处理
const onRendered = () => {
  console.log('文档渲染完成');
  // 不显示成功消息，避免和错误消息冲突
  // ElMessage.success('文档加载完成');
};

const onError = (error: any) => {
  console.error('文档渲染失败:', error);

  // 延迟设置错误状态，避免与渲染成功冲突
  setTimeout(() => {
    // 检查是否是常见的PDF渲染错误
    if (error && error.message && error.message.includes('Transport destroyed')) {
      console.warn('PDF渲染遇到Transport destroyed错误，这通常是正常的');
      return; // 不显示错误，这个错误通常是正常的
    }

    if (error && error.message && error.message.includes('Cannot read properties of undefined')) {
      console.warn('PDF渲染遇到属性读取错误，尝试重新渲染');
      return; // 不显示错误，这个错误通常是临时的
    }

    hasError.value = true;
    errorMessage.value = '文档渲染失败，可能是文件格式不支持或文件已损坏';
    ElMessage.error('文档预览失败');
  }, 1000); // 延迟1秒再显示错误
};

// 重试预览
const retryPreview = () => {
  initPreview();
};

// 下载文件
const downloadFile = async () => {
  try {
    let fileData = actualFileData.value || props.fileData;

    // 如果没有文件数据，先加载
    if (!fileData && props.originalRow?.id) {
      fileData = await loadFileData();
    }

    if (!fileData) {
      throw new Error('没有可下载的文件数据');
    }

    const mimeType = getMimeType(props.fileType);
    const arrayBuffer = base64ToArrayBuffer(fileData);
    const blob = new Blob([arrayBuffer], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = props.fileName || `document.${props.fileType}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
    ElMessage.success('文件下载成功');
  } catch (error) {
    console.error('文件下载失败:', error);
    ElMessage.error('文件下载失败');
  }
};

// 缩放方法
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.05);
  }
};

const zoomOut = () => {
  if (scale.value > 0.25) {
    scale.value = Math.max(0.25, scale.value - 0.05);
  }
};

const resetZoom = () => {
  scale.value = 1;
};

// 组件挂载时初始化预览
onMounted(() => {
  initPreview();
});
</script>

<style lang="scss" scoped>
.vue-office-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 700px; // 限制最大高度，防止弹窗被撑开
  overflow: hidden;

  .preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    // background: #f5f7fa;
    // border-bottom: 1px solid #e4e7ed;
    margin-bottom: 6px;



    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .file-name {
        font-weight: 500;
        color: #fff;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;

      .zoom-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .zoom-display {
          min-width: 60px;
          font-size: 12px;
          color: #606266;
          cursor: default;

          &:hover {
            background-color: transparent !important;
            border-color: #dcdfe6 !important;
            color: #606266 !important;
          }
        }
      }
    }
  }

  .preview-content {
    flex: 1;
    overflow: scroll;
    position: relative;
    height: calc(100% - 60px); // 减去工具栏高度
    min-height: 500px;
    max-height: 600px;
    padding: 20px;
    background: #f5f5f5;

    .document-container {
      height: 100%;
      width: calc(100% - 40px);
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      overflow: auto; // 允许滚动
      position: relative;
      padding: 10px;

      // 确保缩放后的内容可以滚动
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .loading-container,
    .error-container,
    .unsupported-preview,
    .ppt-preview {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 600px;

      .el-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        color: #909399;
        font-size: 16px;
      }
    }

    .image-preview {
      padding: 20px;
      text-align: center;
    }

    .text-preview {
      padding: 20px;
      height: 600px;
      overflow: auto;

      .text-content {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-wrap: break-word;
        margin: 0;
      }
    }

    .pdf-container {
      position: relative;

      // 添加PDF容器的错误处理样式
      :deep(.vue-office-pdf) {
        border: 1px solid #e4e7ed;
        border-radius: 4px;

        // 隐藏可能导致错误的元素
        canvas {
          max-width: 100%;
          height: auto;
        }
      }
    }
  }
}

// vue-office组件样式调整
:deep(.vue-office-docx),
:deep(.vue-office-excel),
:deep(.vue-office-pdf) {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  width: 100% !important;
  height: 100% !important;

  // 确保内容可以滚动
  overflow: auto !important;

  // 确保内容不会超出容器
  box-sizing: border-box !important;

  // 缩放时保持滚动功能
  transform-origin: center top !important;
}
</style>
